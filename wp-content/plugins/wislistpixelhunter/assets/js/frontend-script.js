/**
 * WP Favorites Frontend JavaScript
 * Handles user interactions with performance optimization
 */

(function($) {
    'use strict';

    // Ensure wpFavoritesData is available globally with fallback values
    window.wpFavoritesData = window.wpFavoritesData || {
        ajax_url: '',
        nonce: '',
        isLoggedIn: false,
        userFavorites: [],
        messages: {
            error: {
                login_required: 'Please log in to use favorites',
                add_failed: 'Failed to add to favorites',
                remove_failed: 'Failed to remove from favorites'
            },
            success: {
                added: 'Added to favorites',
                removed: 'Removed from favorites'
            }
        },
        settings: {
            notification_duration: 3000,
            enable_notifications: true
        }
    };

    // Plugin object
    const WPFavorites = {
        
        // Configuration
        config: {
            debounceDelay: 300,
            notificationDuration: 3000,
            maxRetries: 3,
            retryDelay: 1000
        },
        
        // State
        state: {
            isProcessing: false,
            requestQueue: [],
            retryCount: 0
        },
        
        // Cache for DOM elements
        cache: {
            $body: null,
            $toastContainer: null
        },
        
        /**
         * Initialize the plugin
         */
        init: function() {
            // Check if wpFavoritesData is available
            if (typeof wpFavoritesData === 'undefined') {
                console.warn('WP Favorites: wpFavoritesData not found, retrying in 500ms...');
                setTimeout(() => this.init(), 500);
                return;
            }

            this.cache.$body = $('body');
            this.setupToastContainer();
            this.bindEvents();
            this.loadUserFavorites();

            // Initialize existing buttons
            this.initializeFavoriteButtons();

            // Setup observers for dynamic content
            this.setupDynamicContentObserver();

            console.log('WP Favorites initialized successfully');
        },
        
        /**
         * Setup toast notification container
         */
        setupToastContainer: function() {
            if (!$('.wp-favorites-toast-container').length) {
                this.cache.$toastContainer = $('<div class="wp-favorites-toast-container"></div>');
                this.cache.$body.append(this.cache.$toastContainer);
            } else {
                this.cache.$toastContainer = $('.wp-favorites-toast-container');
            }
        },
        
        /**
         * Setup dynamic content observer for AJAX-loaded products
         */
        setupDynamicContentObserver: function() {
            // Watch for new product content being added
            if (window.MutationObserver) {
                const observer = new MutationObserver((mutations) => {
                    let shouldReinitialize = false;

                    mutations.forEach((mutation) => {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            mutation.addedNodes.forEach((node) => {
                                if (node.nodeType === 1) { // Element node
                                    const $node = $(node);
                                    // Check if new products were added - comprehensive detection
                                    if ($node.find('.wp-favorites-button').length > 0 ||
                                        $node.hasClass('product') ||
                                        $node.find('.product').length > 0 ||
                                        $node.hasClass('wc-block-grid__product') ||
                                        $node.find('.wc-block-grid__product').length > 0 ||
                                        $node.hasClass('woocommerce-product') ||
                                        $node.find('.woocommerce-product').length > 0 ||
                                        $node.find('[data-product-id]').length > 0 ||
                                        $node.hasClass('type-product') ||
                                        $node.find('.type-product').length > 0) {
                                        shouldReinitialize = true;
                                    }
                                }
                            });
                        }
                    });

                    if (shouldReinitialize) {
                        // Debounce to prevent excessive reinitializations
                        clearTimeout(this.reinitializeTimeout);
                        this.reinitializeTimeout = setTimeout(() => {
                            this.initializeFavoriteButtons();
                            console.log('WP Favorites: Reinitialized for dynamic content');
                        }, 100);
                    }
                });

                // Observe the entire body for changes
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                // Store observer reference
                this.mutationObserver = observer;
            }

            // Setup comprehensive event listeners for all WooCommerce scenarios
            this.setupWooCommerceEventListeners();
        },

        /**
         * Setup comprehensive WooCommerce event listeners
         */
        setupWooCommerceEventListeners: function() {
            // Standard WooCommerce events
            $(document).on('updated_wc_div', () => {
                setTimeout(() => this.initializeFavoriteButtons(), 100);
            });

            // Cart updates (cross-sells)
            $(document.body).on('updated_cart_totals', () => {
                setTimeout(() => this.initializeFavoriteButtons(), 100);
            });

            // WooCommerce fragments update
            $(document.body).on('wc_fragments_refreshed', () => {
                setTimeout(() => this.initializeFavoriteButtons(), 100);
            });

            // WooCommerce blocks events
            $(document.body).on('wc-blocks_render_blocks_frontend', () => {
                setTimeout(() => this.initializeFavoriteButtons(), 100);
            });

            // Infinite scroll and pagination
            $(document.body).on('post-load', () => {
                setTimeout(() => this.initializeFavoriteButtons(), 100);
            });

            // AJAX pagination
            $(document).on('pjax:complete', () => {
                setTimeout(() => this.initializeFavoriteButtons(), 100);
            });

            // Theme-specific events
            $(document.body).on('elementor/popup/show', () => {
                setTimeout(() => this.initializeFavoriteButtons(), 200);
            });

            // Generic AJAX complete for WooCommerce-related requests
            $(document).ajaxComplete((event, xhr, settings) => {
                if (settings.url && (
                    settings.url.includes('wc-ajax') ||
                    settings.url.includes('woocommerce') ||
                    settings.url.includes('product') ||
                    settings.url.includes('shop') ||
                    settings.url.includes('cart')
                )) {
                    setTimeout(() => {
                        this.initializeFavoriteButtons();
                    }, 150);
                }
            });

            // Window resize (for responsive layouts)
            $(window).on('resize', this.debounce(() => {
                this.initializeFavoriteButtons();
            }, 250));

            // Page visibility change (for when user returns to tab)
            $(document).on('visibilitychange', () => {
                if (!document.hidden) {
                    setTimeout(() => this.initializeFavoriteButtons(), 100);
                }
            });
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Favorite button clicks (delegated for dynamic content) - use multiple event types for better compatibility
            this.cache.$body.on('click.wpFavorites', '.wp-favorites-button', this.debounce(this.handleFavoriteToggle.bind(this), this.config.debounceDelay));
            this.cache.$body.on('touchend.wpFavorites', '.wp-favorites-button', this.debounce(this.handleFavoriteToggle.bind(this), this.config.debounceDelay));

            // Remove from favorites list
            this.cache.$body.on('click.wpFavorites', '.wp-favorites-remove-btn', this.debounce(this.handleFavoriteRemove.bind(this), this.config.debounceDelay));

            // Clear all favorites
            this.cache.$body.on('click.wpFavorites', '.wp-favorites-clear-all', this.handleClearAll.bind(this));

            // Toast close buttons
            this.cache.$body.on('click.wpFavorites', '.wp-favorites-toast-close', this.handleToastClose.bind(this));

            // Handle AJAX errors globally
            $(document).ajaxError(this.handleAjaxError.bind(this));

            // Prevent product link clicks when clicking on favorite buttons
            this.cache.$body.on('click.wpFavorites', '.wp-favorites-wrapper', function(e) {
                e.stopPropagation();
            });
        },
        
        /**
         * Initialize existing favorite buttons with comprehensive duplicate removal
         */
        initializeFavoriteButtons: function() {
            // Remove duplicate buttons first
            this.removeDuplicateButtons();

            // Find all favorite buttons using comprehensive selectors
            const buttonSelectors = [
                '.wp-favorites-button',
                '.wp-favorites-block-button',
                '[data-product-id].wp-favorites-button'
            ].join(', ');

            $(buttonSelectors).each(function() {
                const $button = $(this);
                const productId = $button.data('product-id');

                if (productId && wpFavoritesData && wpFavoritesData.userFavorites) {
                    const isFavorite = wpFavoritesData.userFavorites.includes(productId.toString());
                    $button.toggleClass('is-favorite', isFavorite);

                    // Ensure proper ARIA attributes
                    const title = isFavorite
                        ? (wpFavoritesData.messages?.success?.removed || 'Remove from favorites')
                        : (wpFavoritesData.messages?.success?.added || 'Add to favorites');
                    $button.attr('title', title).attr('aria-label', title);
                }
            });

            // Initialize buttons that might be missing due to timing issues
            this.initializeMissingButtons();
        },

        /**
         * Initialize buttons that might be missing due to timing issues
         */
        initializeMissingButtons: function() {
            // Find product containers without favorite buttons - comprehensive selectors
            const productSelectors = [
                '.woocommerce ul.products li.product',
                '.woocommerce-page ul.products li.product',
                '.products .product',
                '.related.products .product',
                '.cross-sells .product',
                '.up-sells .product',
                '.wc-block-grid__products .wc-block-grid__product',
                '.wp-block-woocommerce-product-grid .wc-block-grid__product',
                '.wp-block-woocommerce-handpicked-products .wc-block-grid__product',
                '.wp-block-woocommerce-product-best-sellers .wc-block-grid__product',
                '.wp-block-woocommerce-product-category .wc-block-grid__product',
                '.wp-block-woocommerce-product-new .wc-block-grid__product',
                '.wp-block-woocommerce-product-on-sale .wc-block-grid__product',
                '.wp-block-woocommerce-product-top-rated .wc-block-grid__product',
                '.wp-block-woocommerce-product-tag .wc-block-grid__product',
                '.product-grid .product',
                '.woocommerce-products .product',
                '.type-product',
                '.post-type-product',
                '.woocommerce-product',
                '.product-item',
                '.shop-item',
                '.wc-product',
                '.elementor-product',
                '.product-loop-item',
                '.woocommerce .product',
                '.woocommerce-page .product',
                'body.woocommerce .product',
                'body.woocommerce-page .product',
                '.woocommerce-loop-product',
                '.wc-product-loop',
                '.product-wrapper',
                '.product-container'
            ].join(', ');

            $(productSelectors).each(function() {
                const $product = $(this);

                // Skip if already has a favorite button
                if ($product.find('.wp-favorites-button').length > 0) {
                    return;
                }

                // Try to extract product ID from various sources
                const productId = $product.data('product-id') ||
                                $product.find('[data-product-id]').first().data('product-id') ||
                                $product.find('a[href*="product"]').first().attr('href')?.match(/product\/[^\/]+\/(\d+)/)?.[1] ||
                                $product.find('a[href*="?p="]').first().attr('href')?.match(/\?p=(\d+)/)?.[1] ||
                                $product.find('.add_to_cart_button').data('product_id') ||
                                $product.find('[data-product_id]').data('product_id') ||
                                $product.attr('data-product-id');

                if (productId) {
                    // Create and inject missing button
                    this.injectMissingButton($product, productId);
                }
            }.bind(this));
        },

        /**
         * Inject missing favorite button
         */
        injectMissingButton: function($product, productId) {
            // Check if wpFavoritesData is available
            if (typeof wpFavoritesData === 'undefined' || !wpFavoritesData) {
                console.warn('WP Favorites: wpFavoritesData not available for button injection');
                return;
            }

            const userFavorites = wpFavoritesData.userFavorites || [];
            const isFavorite = userFavorites.includes(productId.toString());
            const messages = wpFavoritesData.messages || {};
            const title = isFavorite
                ? (messages.success?.removed || 'Remove from favorites')
                : (messages.success?.added || 'Add to favorites');

            const buttonHtml = `
                <div class="wp-favorites-wrapper wp-favorites-injected">
                    <button class="wp-favorites-button ${isFavorite ? 'is-favorite' : ''}"
                            data-product-id="${productId}"
                            title="${title}"
                            aria-label="${title}">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="${isFavorite ? 'currentColor' : 'none'}" stroke="currentColor" stroke-width="2">
                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                        </svg>
                    </button>
                </div>
            `;

            // Find the best place to inject the button
            const $imageContainer = $product.find('.woocommerce-loop-product__link, .wc-block-grid__product-image, .product-image, img').first().parent();

            if ($imageContainer.length > 0) {
                $imageContainer.append(buttonHtml);
            } else {
                $product.prepend(buttonHtml);
            }
        },

        /**
         * Remove duplicate buttons for the same product - comprehensive approach
         */
        removeDuplicateButtons: function() {
            const seenProducts = new Map();

            // Find all favorite buttons using comprehensive selectors
            const buttonSelectors = [
                '.wp-favorites-button',
                '.wp-favorites-block-button',
                '[data-product-id].wp-favorites-button'
            ].join(', ');

            $(buttonSelectors).each(function() {
                const $button = $(this);
                const productId = $button.data('product-id');

                if (!productId) {
                    // Remove buttons without product ID
                    $button.closest('.wp-favorites-wrapper').remove();
                    return;
                }

                // Find the product container using comprehensive selectors
                const $productContainer = $button.closest([
                    '.product',
                    'li.product',
                    '.wc-block-grid__product',
                    '.wp-block-woocommerce-product-grid .wc-block-grid__product',
                    '.product-grid .product',
                    '.woocommerce-products .product',
                    '.type-product',
                    '.post-type-product',
                    '.woocommerce-product',
                    '.product-item',
                    '.shop-item'
                ].join(', '));

                // Create unique key for this product in this specific container
                const containerClass = $productContainer.attr('class') || 'unknown';
                const containerIndex = $productContainer.index();
                const uniqueKey = `${productId}_${containerClass}_${containerIndex}`;

                if (seenProducts.has(uniqueKey)) {
                    // Remove duplicate button
                    $button.closest('.wp-favorites-wrapper').remove();
                    console.log('WP Favorites: Removed duplicate button for product', productId);
                } else {
                    seenProducts.set(uniqueKey, $button);
                }
            });

            // Additional cleanup: remove buttons that are outside product containers
            $('.wp-favorites-button').each(function() {
                const $button = $(this);
                const $productContainer = $button.closest([
                    '.product',
                    'li.product',
                    '.wc-block-grid__product',
                    '.type-product',
                    '.post-type-product'
                ].join(', '));

                if ($productContainer.length === 0) {
                    console.log('WP Favorites: Removed orphaned button');
                    $button.closest('.wp-favorites-wrapper').remove();
                }
            });
        },
        
        /**
         * Handle favorite toggle
         */
        handleFavoriteToggle: function(e) {
            // Prevent all default behaviors and event bubbling
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            const $button = $(e.currentTarget);
            const productId = $button.data('product-id');

            // Additional safety check - ensure we're clicking the button, not a parent element
            if (!$button.hasClass('wp-favorites-button')) {
                return false;
            }

            if (!productId || this.state.isProcessing) {
                return false;
            }

            // Check if wpFavoritesData is available and user is logged in
            if (typeof wpFavoritesData === 'undefined' || !wpFavoritesData) {
                console.error('WP Favorites: wpFavoritesData not available');
                this.showNotification('error', 'Favorites system not available');
                return false;
            }

            if (!wpFavoritesData.isLoggedIn) {
                const errorMessage = wpFavoritesData.messages?.error?.login_required || 'Please log in to use favorites';
                this.showNotification('error', errorMessage);
                return false;
            }

            this.toggleFavorite($button, productId);
            return false;
        },
        
        /**
         * Handle favorite remove from list
         */
        handleFavoriteRemove: function(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const productId = $button.data('product-id');
            
            if (!productId) {
                return;
            }
            
            // Show confirmation if enabled
            if (wpFavoritesData.settings.confirmRemove) {
                if (!confirm(wpFavoritesData.messages.confirm.remove)) {
                    return;
                }
            }
            
            this.removeFavorite($button, productId);
        },
        
        /**
         * Handle clear all favorites
         */
        handleClearAll: function(e) {
            e.preventDefault();
            
            if (!confirm(wpFavoritesData.messages.confirm.clear_all)) {
                return;
            }
            
            this.clearAllFavorites();
        },
        
        /**
         * Toggle favorite status
         */
        toggleFavorite: function($button, productId) {
            this.state.isProcessing = true;
            $button.addClass('is-loading');
            
            const data = {
                action: 'wp_favorites_toggle',
                product_id: productId,
                nonce: wpFavoritesData.nonce
            };
            
            $.ajax({
                url: wpFavoritesData.ajax_url,
                type: 'POST',
                data: data,
                timeout: 10000,
                success: (response) => {
                    this.handleToggleSuccess(response, $button, productId);
                },
                error: (xhr, status, error) => {
                    this.handleToggleError(xhr, status, error, $button, productId);
                },
                complete: () => {
                    this.state.isProcessing = false;
                    $button.removeClass('is-loading');
                }
            });
        },
        
        /**
         * Handle toggle success
         */
        handleToggleSuccess: function(response, $button, productId) {
            if (response.success) {
                const isNowFavorite = response.data.is_favorite;
                
                // Update button state
                $button.toggleClass('is-favorite', isNowFavorite);
                
                // Update button title
                const title = isNowFavorite ? 
                    wp.i18n.__('Remove from favorites', 'wp-favorites') : 
                    wp.i18n.__('Add to favorites', 'wp-favorites');
                $button.attr('title', title).attr('aria-label', title);
                
                // Show notification
                this.showNotification('success', response.data.message);
                
                // Update local cache
                this.updateLocalCache(productId, isNowFavorite);
                
                // Trigger custom event
                this.cache.$body.trigger('wp_favorites_toggled', {
                    productId: productId,
                    isFavorite: isNowFavorite,
                    action: response.data.action
                });
                
            } else {
                this.showNotification('error', response.data.message || wpFavoritesData.messages.error.add_failed);
            }
        },
        
        /**
         * Handle toggle error
         */
        handleToggleError: function(xhr, status, error, $button, productId) {
            let message = wpFavoritesData.messages.error.add_failed;
            
            if (xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.message) {
                message = xhr.responseJSON.data.message;
            } else if (status === 'timeout') {
                message = wp.i18n.__('Request timed out. Please try again.', 'wp-favorites');
            } else if (status === 'error' && xhr.status === 0) {
                message = wp.i18n.__('Network error. Please check your connection.', 'wp-favorites');
            }
            
            this.showNotification('error', message);
            
            // Retry logic
            if (this.state.retryCount < this.config.maxRetries) {
                this.state.retryCount++;
                setTimeout(() => {
                    this.toggleFavorite($button, productId);
                }, this.config.retryDelay);
            } else {
                this.state.retryCount = 0;
            }
        },
        
        /**
         * Remove favorite from list
         */
        removeFavorite: function($button, productId) {
            const $item = $button.closest('.wp-favorites-item');
            $item.addClass('is-removing');
            
            const data = {
                action: 'wp_favorites_remove',
                product_id: productId,
                nonce: wpFavoritesData.nonce
            };
            
            $.ajax({
                url: wpFavoritesData.ajax_url,
                type: 'POST',
                data: data,
                success: (response) => {
                    if (response.success) {
                        // Animate removal
                        $item.fadeOut(300, function() {
                            $(this).remove();
                            
                            // Check if list is empty
                            if (!$('.wp-favorites-item').length) {
                                this.showEmptyState();
                            }
                        }.bind(this));
                        
                        this.showNotification('success', response.data.message);
                        
                        // Update all favorite buttons for this product
                        $(`.wp-favorites-button[data-product-id="${productId}"]`).removeClass('is-favorite');
                        
                        // Update local cache
                        this.updateLocalCache(productId, false);
                        
                    } else {
                        $item.removeClass('is-removing');
                        this.showNotification('error', response.data.message || wpFavoritesData.messages.error.remove_failed);
                    }
                },
                error: () => {
                    $item.removeClass('is-removing');
                    this.showNotification('error', wpFavoritesData.messages.error.remove_failed);
                }
            });
        },
        
        /**
         * Clear all favorites
         */
        clearAllFavorites: function() {
            const data = {
                action: 'wp_favorites_clear_all',
                nonce: wpFavoritesData.nonce
            };
            
            $.ajax({
                url: wpFavoritesData.ajax_url,
                type: 'POST',
                data: data,
                success: (response) => {
                    if (response.success) {
                        $('.wp-favorites-item').fadeOut(300, function() {
                            $(this).remove();
                        });
                        
                        setTimeout(() => {
                            this.showEmptyState();
                        }, 300);
                        
                        this.showNotification('success', response.data.message);
                        
                        // Update all favorite buttons
                        $('.wp-favorites-button').removeClass('is-favorite');
                        
                        // Clear local cache
                        if (wpFavoritesData.userFavorites) {
                            wpFavoritesData.userFavorites = [];
                        }
                        
                    } else {
                        this.showNotification('error', response.data.message || wp.i18n.__('Failed to clear favorites', 'wp-favorites'));
                    }
                },
                error: () => {
                    this.showNotification('error', wp.i18n.__('Failed to clear favorites', 'wp-favorites'));
                }
            });
        },
        
        /**
         * Show empty state
         */
        showEmptyState: function() {
            const emptyHtml = `
                <div class="wp-favorites-empty">
                    <div class="wp-favorites-empty-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                        </svg>
                    </div>
                    <h3 class="wp-favorites-empty-title">${wpFavoritesData.messages.info.empty_list}</h3>
                    <p class="wp-favorites-empty-message">${wp.i18n.__('Start adding products to your favorites to see them here.', 'wp-favorites')}</p>
                    <a href="${wpFavoritesData.shopUrl || '/'}" class="wp-favorites-empty-action">
                        ${wp.i18n.__('Browse Products', 'wp-favorites')}
                    </a>
                </div>
            `;
            
            $('.wp-favorites-list').html(emptyHtml);
        },
        
        /**
         * Load user favorites
         */
        loadUserFavorites: function() {
            if (!wpFavoritesData.isLoggedIn) {
                return;
            }
            
            // This would typically be loaded server-side, but can be refreshed via AJAX if needed
            const data = {
                action: 'wp_favorites_get_list',
                nonce: wpFavoritesData.nonce
            };
            
            // Only load if not already provided
            if (!wpFavoritesData.userFavorites) {
                $.ajax({
                    url: wpFavoritesData.ajax_url,
                    type: 'POST',
                    data: data,
                    success: (response) => {
                        if (response.success && response.data.products) {
                            wpFavoritesData.userFavorites = response.data.products.map(p => p.id.toString());
                            this.initializeFavoriteButtons();
                        }
                    }
                });
            }
        },
        
        /**
         * Update local cache
         */
        updateLocalCache: function(productId, isFavorite) {
            if (!wpFavoritesData.userFavorites) {
                wpFavoritesData.userFavorites = [];
            }
            
            const productIdStr = productId.toString();
            const index = wpFavoritesData.userFavorites.indexOf(productIdStr);
            
            if (isFavorite && index === -1) {
                wpFavoritesData.userFavorites.push(productIdStr);
            } else if (!isFavorite && index > -1) {
                wpFavoritesData.userFavorites.splice(index, 1);
            }
        },
        
        /**
         * Show notification
         */
        showNotification: function(type, message) {
            if (!wpFavoritesData.settings.enable_notifications) {
                return;
            }
            
            const icons = {
                success: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="20,6 9,17 4,12"></polyline></svg>',
                error: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>',
                info: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>',
                warning: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>'
            };
            
            const $toast = $(`
                <div class="wp-favorites-toast ${type}">
                    <div class="wp-favorites-toast-content">
                        <div class="wp-favorites-toast-icon ${type}">${icons[type] || icons.info}</div>
                        <div class="wp-favorites-toast-message">${message}</div>
                    </div>
                    <button class="wp-favorites-toast-close" aria-label="${wp.i18n.__('Close notification', 'wp-favorites')}">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
            `);
            
            this.cache.$toastContainer.append($toast);
            
            // Trigger animation
            setTimeout(() => {
                $toast.addClass('show');
            }, 10);
            
            // Auto remove
            const duration = wpFavoritesData.settings.notification_duration || this.config.notificationDuration;
            setTimeout(() => {
                this.removeToast($toast);
            }, duration);
        },
        
        /**
         * Handle toast close
         */
        handleToastClose: function(e) {
            e.preventDefault();
            const $toast = $(e.currentTarget).closest('.wp-favorites-toast');
            this.removeToast($toast);
        },
        
        /**
         * Remove toast notification
         */
        removeToast: function($toast) {
            $toast.removeClass('show');
            setTimeout(() => {
                $toast.remove();
            }, 300);
        },
        
        /**
         * Handle global AJAX errors
         */
        handleAjaxError: function(event, xhr, settings, error) {
            // Only handle our AJAX requests
            if (settings.data && typeof settings.data === 'string' && settings.data.includes('wp_favorites_')) {
                console.error('WP Favorites AJAX Error:', error, xhr);
            }
        },
        
        /**
         * Debounce function
         */
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func.apply(this, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };
    
    // Initialize when DOM is ready
    $(document).ready(function() {
        // Check if wpFavoritesData is available
        if (typeof wpFavoritesData !== 'undefined') {
            WPFavorites.init();
        } else {
            console.warn('WP Favorites: wpFavoritesData not found');
        }
    });
    
    // Expose to global scope for external access
    window.WPFavorites = WPFavorites;
    
})(jQuery);
