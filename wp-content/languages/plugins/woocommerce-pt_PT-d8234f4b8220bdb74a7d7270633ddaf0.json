{"translation-revision-date": "2025-07-23 18:00:27+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "pt"}, "Displays the PHP product search results.": ["Apresenta os resultados da pesquisa de produtos PHP."], "Product Search Results (Classic)": ["Resultados da pesquisa de produtos (Clássico)"], "Product Tag (Classic)": ["Etiqueta de produto (Clássico)"], "Product Grid (Classic)": ["Grelha de produtos (Clássico)"], "Product Category (Classic)": ["Categoria de produto (Clássico)"], "Displays the PHP product grid page. ": ["Exibe a página PHP da grelha de produtos. "], "Product's Custom Taxonomy (Classic)": ["Taxonomia personalizada de produto (Clássica)"], "Displays the PHP product attribute page.": ["Exibe a página PHP de atributos de produto."], "Displays the PHP product tag page.": ["Exibe a página PHP de etiqueta de produto."], "Displays the PHP product category page.": ["Exibe a página PHP da categoria de produto."], "Displays the PHP product page.": ["Exibe a página PHP de produto."], "Product Attribute (Classic)": ["Atributo de produto (Clássico)"], "Displays the PHP product's custom taxonomy page.": ["Exibe a página PHP de taxonomia personalizada de produto."], "Product (Classic)": ["Produto (Clássico)"], "WooCommerce Classic Template": ["Modelo clássico de WooCommerce"], "Undo": ["<PERSON><PERSON>"], "Order number": ["Número de encomenda"], "Search products…": ["Pesquisar produtos…"], "Order details": ["<PERSON><PERSON><PERSON> da encomenda"], "Subtotal": ["Subtotal"], "Thank you. Your order has been received.": ["Obrigado. A sua encomenda foi recebida."], "Shipping address": ["Morada de envio"], "Billing address": ["Morada de facturação"], "No products were found matching your selection.": ["Não foram encontrados produtos correspondentes à sua pesquisa."], "Order received": ["Encomenda recebida"], "Date": ["Data"], "Total": ["Total"], "Email": ["Email"], "Search": ["<PERSON><PERSON><PERSON><PERSON>"], "WooCommerce": ["WooCommerce"], "Product": ["Produ<PERSON>", "<PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/legacy-template.js"}}