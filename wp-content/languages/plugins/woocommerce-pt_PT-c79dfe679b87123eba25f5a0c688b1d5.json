{"translation-revision-date": "2025-07-23 18:00:27+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "pt"}, "Single product": ["Produto simples"], "Item sold": ["<PERSON>em vendido", "<PERSON>ens vendidos"], "A sentence describing filters for Products. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ\u0004Products Match <select/> Filters": ["Produtos correspondem a <select/> os filtros"], "Type to search for a variation": ["Digite para pesquisar uma variação"], "Single variation": ["Variação única"], "Check at least two variations below to compare": ["Escolha pelo menos duas variações abaixo para comparar"], "Check at least two products below to compare": ["Escolha pelo menos dois produtos abaixo para comparar"], "net sales": ["vendas líquidas"], "item sold": ["item vendido", "itens vendidos"], "%d products": ["%d produtos"], "%d variations": ["%d variações"], "categories\u0004+%d more": ["+%d mais"], "All variations": ["<PERSON><PERSON> as variações"], "Advanced Filters": ["Filtros a<PERSON>"], "Search by variation name or SKU": ["Pesquisar por nome da variação ou REF"], "Search by product name or SKU": ["Pesquisar por nome de produto ou REF"], "Search for variations to compare": ["Pesquisar por variações para comparar"], "Search for products to compare": ["Pesquisar por produtos para comparar"], "Comparison": ["Comparação"], "variation sold": ["variação vendida", "variações vendidas"], "Indication of a low quantity\u0004Low": ["Baixa"], "(Deleted)": ["(Eliminado)"], "Product / Variation title": ["Nome do produto / variação"], "Net sales": ["Vendas líquidas"], "Items sold": ["<PERSON>ens vendidos"], "Compare": ["Comparar"], "Product title": ["Título do produto"], "Category": ["Categoria", "Categorias"], "All products": ["Todos os produtos"], "Show": ["Mostrar"], "orders": ["encomenda", "encomendas"], "Products": ["<PERSON><PERSON><PERSON>"], "Variations": ["Variações"], "N/A": ["n.d."], "SKU": ["REF"], "Stock": ["Stock"], "Status": ["Estado"], "Order": ["Encomenda", "Encomendas"], "Orders": ["Encomendas"], "Product": ["Produ<PERSON>", "<PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/admin/chunks/analytics-report-products.js"}}