{"translation-revision-date": "2025-07-23 18:00:27+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "pt"}, "Flat rate shipping": ["Envio com taxa fixa"], "T-Shirt": ["T-shirt"], "Start shopping": ["<PERSON><PERSON><PERSON> a comprar"], "Empty Mini-Cart": ["Minicarrinho vazio"], "Filled Mini-Cart": ["<PERSON>car<PERSON><PERSON> cheio"], "Mini-Cart Contents": ["Conteúdos do minicarrinho"], "Go to checkout": ["Ir para a finalização de compras"], "View my cart": ["<PERSON><PERSON><PERSON><PERSON>"], "Shipping, taxes, and discounts calculated at checkout.": ["Envio, impostos e descontos calculados na finalização de compras."], "Your cart": ["O seu carrinho"], "%s has been removed from your cart.": ["%s foi removido do seu carrinho."], "Display a Mini-Cart widget.": ["Mostra um widget de minicarrinho"], "Price between %1$s and %2$s": ["Preço entre %1$s e %2$s"], "%s (optional)": ["%s (opcional)"], "Details": ["<PERSON><PERSON><PERSON>"], "Orange": ["<PERSON><PERSON>"], "Yellow": ["<PERSON><PERSON>"], "Cap": ["<PERSON><PERSON>"], "Beanie": ["<PERSON><PERSON>"], "example product in Cart Block\u0004Beanie": ["<PERSON><PERSON>"], "example product in Cart Block\u0004Beanie with Logo": ["Gorro com logotipo"], "Remove item": ["Remover este item"], "Quantity of %s in your cart.": ["Quantidade de %s no seu carrinho."], "Discounted price:": ["Preço com desconto:"], "Previous price:": ["Preço anterior:"], "%d left in stock": ["%d ainda em stock"], "Color": ["Cor"], "Small": ["Pequeno"], "Size": ["<PERSON><PERSON><PERSON>"], "Free shipping": ["<PERSON><PERSON>"], "Subtotal": ["Subtotal"], "Dimensions": ["Dimensões (C x L x A)"], "Shipping": ["<PERSON><PERSON>"], "Save %s": ["Poupa %s"], "Total": ["Total"], "WooCommerce": ["WooCommerce"], "Local pickup": ["Levantamento no local"], "Available on backorder": ["Disponível por encomenda"], "Fee": ["Custo adicional"], "Product": ["Produ<PERSON>", "<PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/mini-cart-contents.js"}}