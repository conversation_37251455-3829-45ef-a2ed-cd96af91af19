<?php
/**
 * Test script to validate WP Favorites fixes
 * Add ?test_favorites=1 to any page to run tests
 */

if (!defined('ABSPATH')) {
    exit;
}

// Only run if test parameter is present and user is admin
if (isset($_GET['test_favorites']) && current_user_can('manage_options')) {
    add_action('wp_footer', 'wp_favorites_test_fixes');
}

function wp_favorites_test_fixes() {
    ?>
    <script>
    console.log('=== WP Favorites Test Suite ===');
    
    // Test 1: Check if wpFavoritesData is available
    if (typeof wpFavoritesData !== 'undefined') {
        console.log('✓ wpFavoritesData is available');
        console.log('Data:', wpFavoritesData);
    } else {
        console.log('✗ wpFavoritesData is NOT available');
    }
    
    // Test 2: Check if WPFavorites object is initialized
    if (typeof WPFavorites !== 'undefined') {
        console.log('✓ WPFavorites object is available');
    } else {
        console.log('✗ WPFavorites object is NOT available');
    }
    
    // Test 3: Check for favorite buttons
    const buttons = document.querySelectorAll('.wp-favorites-button');
    console.log(`Found ${buttons.length} favorite buttons`);
    
    if (buttons.length > 0) {
        console.log('✓ Favorite buttons found');
        
        // Test button positioning
        buttons.forEach((button, index) => {
            const rect = button.getBoundingClientRect();
            const wrapper = button.closest('.wp-favorites-wrapper');
            const product = button.closest('.product, .wc-block-grid__product, .type-product');
            
            console.log(`Button ${index + 1}:`, {
                position: window.getComputedStyle(button).position,
                zIndex: window.getComputedStyle(button).zIndex,
                visible: rect.width > 0 && rect.height > 0,
                hasWrapper: !!wrapper,
                hasProduct: !!product,
                productId: button.dataset.productId
            });
        });
    } else {
        console.log('✗ No favorite buttons found');
    }
    
    // Test 4: Check CSS loading
    const stylesheets = Array.from(document.styleSheets);
    const wpFavoritesCSS = stylesheets.find(sheet => 
        sheet.href && sheet.href.includes('wp-favorites') || 
        sheet.href && sheet.href.includes('frontend-style')
    );
    
    if (wpFavoritesCSS) {
        console.log('✓ WP Favorites CSS is loaded');
    } else {
        console.log('✗ WP Favorites CSS is NOT loaded');
    }
    
    // Test 5: Test click event handling
    if (buttons.length > 0) {
        console.log('Testing click event handling...');
        
        // Add test click handler
        buttons[0].addEventListener('click', function(e) {
            console.log('✓ Click event fired on favorite button');
            console.log('Event details:', {
                defaultPrevented: e.defaultPrevented,
                propagationStopped: e.cancelBubble,
                target: e.target,
                currentTarget: e.currentTarget
            });
        });
        
        // Simulate click
        setTimeout(() => {
            console.log('Simulating click on first button...');
            buttons[0].click();
        }, 1000);
    }
    
    // Test 6: Check for product containers
    const productContainers = document.querySelectorAll(`
        .woocommerce ul.products li.product,
        .woocommerce-page ul.products li.product,
        .products .product,
        .wc-block-grid__product,
        .type-product
    `);
    
    console.log(`Found ${productContainers.length} product containers`);
    
    if (productContainers.length > 0) {
        console.log('✓ Product containers found');
        
        // Check positioning
        productContainers.forEach((container, index) => {
            const position = window.getComputedStyle(container).position;
            console.log(`Product container ${index + 1} position: ${position}`);
        });
    } else {
        console.log('✗ No product containers found');
    }
    
    console.log('=== Test Suite Complete ===');
    </script>
    
    <style>
    /* Test styles to highlight issues */
    .wp-favorites-button {
        border: 2px solid red !important;
        background: yellow !important;
    }
    
    .wp-favorites-wrapper {
        border: 1px dashed blue !important;
    }
    
    .product, .wc-block-grid__product, .type-product {
        border: 1px solid green !important;
    }
    </style>
    
    <div style="position: fixed; top: 10px; left: 10px; background: white; padding: 10px; border: 1px solid #ccc; z-index: 9999; max-width: 300px;">
        <h4>WP Favorites Test Mode</h4>
        <p>Check browser console for test results.</p>
        <p>Buttons highlighted in yellow/red, wrappers in blue, products in green.</p>
        <button onclick="location.reload()">Reload Page</button>
    </div>
    <?php
}
