.bd-copy-button {
  appearance: none;
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 99;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 500;
  white-space: nowrap;
  text-indent: -9999px;
  color: black;
  background-color: #FFC514;
  box-shadow: rgba(0, 0, 0, 0.1) 0 1px 3px 0,
              rgba(0, 0, 0, 0.1) 0 1px 2px -1px;
  border: none;
  cursor: pointer;
  padding: 15px;
  line-height: 1;
  border-radius: 9999px;
  opacity: 0;
  pointer-events: none;
  transition: .3s opacity ease, .3s transform ease;
  text-transform: lowercase;
}

.bd-copy-button:before {
  content: '';
  display: inline-block;
  width: 20px;
  height: 18px;
  background-color: black;
  mask-image: url("data:image/svg+xml,%3Csvg stroke='%23475569' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 16'%3E%3Cpath d='M4 2.75H2.75c-1.1 0-2 .9-2 2v8.5c0 1.1.9 2 2 2h8.5c1.1 0 2-.9 2-2v-8.5c0-1.1-.9-2-2-2H10' fill='none' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5'/%3E%3Cpath d='M9 4.25H5c-.55 0-1-.45-1-1v-1.5c0-.55.45-1 1-1h4c.55 0 1 .45 1 1v1.5c0 .55-.45 1-1 1ZM4.75 8.25h4.5M4.75 11.25h4.5' fill='none' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5'/%3E%3C/svg%3E");
  mask-size: contain;
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg stroke='%23475569' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 16'%3E%3Cpath d='M4 2.75H2.75c-1.1 0-2 .9-2 2v8.5c0 1.1.9 2 2 2h8.5c1.1 0 2-.9 2-2v-8.5c0-1.1-.9-2-2-2H10' fill='none' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5'/%3E%3Cpath d='M9 4.25H5c-.55 0-1-.45-1-1v-1.5c0-.55.45-1 1-1h4c.55 0 1 .45 1 1v1.5c0 .55-.45 1-1 1ZM4.75 8.25h4.5M4.75 11.25h4.5' fill='none' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5'/%3E%3C/svg%3E");
}

/* Add to page button */

.bd-copy-button--import {
  border-radius: 3px;
  width: auto;
  text-indent: 0;
  gap: 5px;
  padding: 15px 30px;
  line-height: 1.5;
}

.bd-copy-button--import:before {
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512' class=''%3E%3Cpath fill='%23475569' d='M24 80.03h336c13.25 0 24-10.76 24-24.02S373.3 32 360 32H24C10.75 32 0 42.76 0 56.02s10.75 24.01 24 24.01zM81.47 304.2L168 212.1v243.8c0 13.3 10.8 24.1 24 24.1s24-10.77 24-24.04V212.1l86.53 92.05c4.77 5.05 11.07 7.55 17.47 7.55 5.906 0 11.81-2.16 16.44-6.541 9.656-9.076 10.12-24.29 1.031-33.96l-128-136.2c-9.062-9.702-25.88-9.702-34.94 0L46.53 271.2c-9.09 9.7-8.62 24.9 1.03 34 9.63 9.1 24.82 8.7 33.91-1z'%3E%3C/path%3E%3C/svg%3E");
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512' class=''%3E%3Cpath fill='%23475569' d='M24 80.03h336c13.25 0 24-10.76 24-24.02S373.3 32 360 32H24C10.75 32 0 42.76 0 56.02s10.75 24.01 24 24.01zM81.47 304.2L168 212.1v243.8c0 13.3 10.8 24.1 24 24.1s24-10.77 24-24.04V212.1l86.53 92.05c4.77 5.05 11.07 7.55 17.47 7.55 5.906 0 11.81-2.16 16.44-6.541 9.656-9.076 10.12-24.29 1.031-33.96l-128-136.2c-9.062-9.702-25.88-9.702-34.94 0L46.53 271.2c-9.09 9.7-8.62 24.9 1.03 34 9.63 9.1 24.82 8.7 33.91-1z'%3E%3C/path%3E%3C/svg%3E");
  background-color: black;
}

/* Success button */
.bd-copy-button--success {
  color: white;
  background-color: #22c55e;
}

.bd-copy-button--success:before {
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='48' width='48'%3E%3Cpath fill='%23fff' d='M18.9 35.7 7.7 24.5l2.15-2.15 9.05 9.05 19.2-19.2 2.15 2.15Z'/%3E%3C/svg%3E");
  mask-size: 25px 25px;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='48' width='48'%3E%3Cpath fill='%23fff' d='M18.9 35.7 7.7 24.5l2.15-2.15 9.05 9.05 19.2-19.2 2.15 2.15Z'/%3E%3C/svg%3E");
  -webkit-mask-size: 25px 25px;
  background-color: white;
}

/* Element Outline */
[data-element]:hover {
  outline-offset: -1px;
  outline: 1px solid rgb(177, 202, 255);
}

[data-element]:hover .bd-copy-button {
  opacity: 1;
  pointer-events: auto;
}

.is-external-link {
  cursor: not-allowed!important;
}
