{"translation-revision-date": "2025-07-23 18:00:27+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "pt"}, "Max. Price": ["Preço máximo"], "Min. Price": ["Preço mínimo"], "Show 'Apply filters' button": ["Mostrar botão 'Aplicar filtros'"], "Show input fields inline with the slider.": ["Mostrar campos em linha com o slider."], "Price Range Selector": ["Selector de intervalo de preços"], "Filter by Price": ["Filtrar por preço"], "Inline input fields": ["Campos em linha"], "Reset price filter": ["Repor filtro de preço"], "Reset filter": ["Repor filtro"], "Block title": ["Título do bloco"], "Apply filter": ["Aplicar filtro"], "Editable": ["<PERSON><PERSON><PERSON>"], "Products will update when the button is clicked.": ["Os produtos serão actualizados quando clicar no botão."], "Filter products by minimum price": ["Filtrar os produtos por preço mínimo"], "Display a slider to filter products in your store by price.": ["Mostra um slider para filtrar produtos na sua loja por preço."], "Text": ["Texto"], "Filter by price": ["Filtrar por preço"], "Add new product": ["Novo produto"], "Learn more": ["Aprenda mais"], "Reset": ["Repor"], "Apply": ["Aplicar"], "Settings": ["Configurações"]}}, "comment": {"reference": "assets/client/blocks/price-filter.js"}}